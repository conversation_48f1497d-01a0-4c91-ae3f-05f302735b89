# Laravel项目Apache虚拟主机配置示例
# 将此配置添加到小皮面板的Apache vhosts.conf文件中

<VirtualHost *:80>
    # 替换为你的域名或IP地址
    ServerName 你的域名或IP地址
    
    # Laravel项目的public目录（重要！）
    DocumentRoot "D:/wangzhan/flyXxtt2/public"
    
    <Directory "D:/wangzhan/flyXxtt2/public">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # 启用重写引擎
        RewriteEngine On
        
        # Laravel URL重写规则
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>
    
    # 错误和访问日志
    ErrorLog "logs/xxtt2_error.log"
    CustomLog "logs/xxtt2_access.log" common
</VirtualHost>

# 如果需要HTTPS，添加以下配置：
<VirtualHost *:443>
    ServerName 你的域名
    DocumentRoot "D:/wangzhan/flyXxtt2/public"
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile "你的证书路径.crt"
    SSLCertificateKeyFile "你的私钥路径.key"
    
    <Directory "D:/wangzhan/flyXxtt2/public">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>
</VirtualHost>
