<?php
// Apache重写模块测试文件
echo "<h2>Apache重写模块测试</h2>";

// 检查重写模块
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✅ mod_rewrite 模块已启用</p>";
    } else {
        echo "<p style='color: red;'>❌ mod_rewrite 模块未启用</p>";
    }
    
    echo "<h3>已加载的Apache模块：</h3>";
    echo "<ul>";
    foreach ($modules as $module) {
        echo "<li>$module</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: orange;'>⚠️ 无法检测Apache模块（可能不是Apache环境）</p>";
}

// 检查.htaccess文件
$htaccess_path = __DIR__ . '/.htaccess';
if (file_exists($htaccess_path)) {
    echo "<p style='color: green;'>✅ .htaccess 文件存在</p>";
    echo "<h3>.htaccess 内容：</h3>";
    echo "<pre>" . htmlspecialchars(file_get_contents($htaccess_path)) . "</pre>";
} else {
    echo "<p style='color: red;'>❌ .htaccess 文件不存在</p>";
}

// 测试URL重写
echo "<h3>URL重写测试：</h3>";
echo "<p>如果重写正常工作，以下链接应该可以访问：</p>";
echo "<ul>";
echo "<li><a href='/login'>登录页面 (/login)</a></li>";
echo "<li><a href='/register'>注册页面 (/register)</a></li>";
echo "<li><a href='/home'>首页 (/home)</a></li>";
echo "</ul>";

echo "<p><strong>如果上面的链接返回404错误，说明URL重写没有正常工作。</strong></p>";
?>
